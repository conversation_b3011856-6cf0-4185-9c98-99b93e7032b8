/**
 * Admin Subscription Components
 * 
 * Centralized exports for all subscription management components.
 * These components provide comprehensive subscription monitoring and management
 * capabilities for store owners within the admin dashboard.
 * 
 * Created: 2025-01-16
 * Part of: Admin Dashboard Implementation - Phase 2
 */

// Core subscription dashboard components
export { default as SubscriptionOverviewCard } from './SubscriptionOverviewCard';
export { default as ProblematicUsersTable } from './ProblematicUsersTable';
export { default as SystemHealthMonitor } from './SystemHealthMonitor';

// Re-export component types for external use
export type { default as SubscriptionOverviewCardProps } from './SubscriptionOverviewCard';
export type { default as ProblematicUsersTableProps } from './ProblematicUsersTable';
export type { default as SystemHealthMonitorProps } from './SystemHealthMonitor';

// Component collection for easy dashboard integration
export const SubscriptionComponents = {
  OverviewCard: SubscriptionOverviewCard,
  ProblematicUsersTable: ProblematicUsersTable,
  HealthMonitor: SystemHealthMonitor,
} as const;

/**
 * Usage Example:
 * 
 * import { 
 *   SubscriptionOverviewCard, 
 *   ProblematicUsersTable, 
 *   SystemHealthMonitor 
 * } from '@/components/admin/subscription';
 * 
 * // Or use the collection:
 * import { SubscriptionComponents } from '@/components/admin/subscription';
 * const { OverviewCard, ProblematicUsersTable, HealthMonitor } = SubscriptionComponents;
 */
